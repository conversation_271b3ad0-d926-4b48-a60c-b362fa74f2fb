'use client';
import { motion } from 'framer-motion';
import TextReveal from '@/components/TextReveal';
import Rounded from '@/common/RoundedButton';
import Separator from '@/components/Separator';
import styles from './style.module.scss';

export default function Hero3({
  label,
  title,
  description,
  showDescription = true,
  buttonText,
  buttonLink,
  className = '',
  labelDelay = 0.3,
  descriptionDelay = 0.4,
  buttonDelay = 0.5
}) {
  return (
    <div>
                <Separator
                  animated={true}
                  color="#000"
                  thickness={1}
                  className={styles.separator}
                />
                
    <div className={`${styles.header} ${className}`}>
      <div className={styles.leftSection}>
        <motion.p
          className={`text-big ${styles.sectionLabel}`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, ease: 'easeInOut', delay: labelDelay }}
        >
          {label}
        </motion.p>
      </div>

      <div className={styles.rightSection}>
        <TextReveal as="h2" className={styles.sectionTitle}>
          {title}
        </TextReveal>
        
        {description && showDescription && (
          <motion.p
            className={styles.description}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: 'easeInOut', delay: descriptionDelay }}
          >
            {description}
          </motion.p>
        )}

        {buttonText && buttonLink && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, ease: 'easeInOut', delay: buttonDelay }}
          >
            <Rounded href={buttonLink} className={styles.viewAllButton}>
              <p>{buttonText}</p>
            </Rounded>
          </motion.div>
        )}
      </div>
    </div>
    </div>
  );
}
